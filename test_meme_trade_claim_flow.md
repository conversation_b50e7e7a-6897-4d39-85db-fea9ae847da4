# Test Plan: Meme Trade Claim Flow

## Overview
This document outlines the testing plan for the new Meme trade task claim flow where users must manually claim rewards instead of receiving points automatically.

## Changes Made

### 1. Task Processors Updated
- `DailyTaskProcessor.processMemeTrade()` - Now only marks task as COMPLETED
- `TradingTaskProcessor.processMemeTrade()` - Now only marks task as COMPLETED
- `MemeTradeHandler.Handle()` - Now only marks task as COMPLETED
- `TradingMemeTradeHandler.Handle()` - Now only marks task as COMPLETED

### 2. Service Layer Updated
- `TaskManagementService.CompleteTask()` - Checks if task is Meme trade, if so doesn't award points immediately
- `TaskManagementService.ClaimTaskReward()` - Now awards points when claiming

### 3. GraphQL Layer Updated
- `CompleteTask` resolver - Returns 0 points for Meme trade tasks with appropriate message
- `ClaimTaskReward` resolver - Returns actual points awarded when claiming

## Test Scenarios

### Scenario 1: Meme Trade Task Completion via NATS Event
**Expected Flow:**
1. User makes a Meme trade
2. NATS event triggers task processor
3. Task status changes: NOT_STARTED → COMPLETED
4. No points awarded yet
5. User can see task is ready to claim

**Test Steps:**
1. Create a test user
2. Ensure user has Meme trade daily task
3. Simulate NATS event with trade data
4. Verify task status is COMPLETED
5. Verify user points haven't increased
6. Verify task shows as claimable in UI

### Scenario 2: Manual Task Claim
**Expected Flow:**
1. User has COMPLETED Meme trade task
2. User calls claimTaskReward API
3. Points are awarded
4. Task status changes: COMPLETED → CLAIMED
5. Tier upgrade check is performed

**Test Steps:**
1. Start with user having COMPLETED Meme trade task
2. Record user's current points and tier
3. Call claimTaskReward GraphQL mutation
4. Verify points increased by task.Points
5. Verify task status is CLAIMED
6. Verify tier upgrade if applicable

### Scenario 3: Non-Meme Trade Task (Backward Compatibility)
**Expected Flow:**
1. User completes non-Meme trade task (e.g., daily check-in)
2. Points awarded immediately
3. Task status changes: NOT_STARTED → COMPLETED
4. No need to claim

**Test Steps:**
1. Create test user
2. Complete daily check-in task
3. Verify points awarded immediately
4. Verify task status is COMPLETED
5. Verify claimTaskReward is not needed

### Scenario 4: Error Cases
**Test Cases:**
1. Try to claim task that's not COMPLETED
2. Try to claim task that's already CLAIMED
3. Try to claim non-existent task
4. Try to complete already completed Meme trade task

## GraphQL Mutations for Testing

### Complete Meme Trade Task
```graphql
mutation {
  completeTask(input: { 
    taskId: "meme-trade-task-id", 
    verificationData: "{\"volume\": 100.0, \"trade_type\": \"MEME\"}" 
  }) {
    success
    message
    pointsAwarded  # Should be 0 for Meme trade tasks
    tierUpgraded
  }
}
```

### Claim Task Reward
```graphql
mutation {
  claimTaskReward(input: { taskId: "meme-trade-task-id" }) {
    success
    message
    pointsClaimed  # Should be actual task points
  }
}
```

### Check Task Status
```graphql
query {
  taskCenter {
    success
    data {
      categories {
        tasks {
          id
          name
          points
          userProgress {
            status  # Should show COMPLETED before claim, CLAIMED after
            canBeClaimed  # Should be true for COMPLETED Meme trade tasks
            pointsEarned
          }
        }
      }
    }
  }
}
```

## Expected Database Changes

### user_task_progress Table
- Status flow: NOT_STARTED → COMPLETED → CLAIMED
- PointsEarned: 0 when COMPLETED, task.Points when CLAIMED
- LastCompletedAt: Set when task becomes COMPLETED
- UpdatedAt: Updated on both completion and claim

### task_completion_history Table
- Entry created when task is claimed (not when completed)
- Points recorded when claimed

### user_tier_progress Table
- Points added only when task is claimed
- Tier upgrades happen only on claim

## Validation Checklist

- [ ] Meme trade tasks don't award points on completion
- [ ] Meme trade tasks award points on claim
- [ ] Non-Meme trade tasks still work as before
- [ ] Task status transitions correctly
- [ ] GraphQL responses are accurate
- [ ] Tier upgrades work correctly
- [ ] Error handling works properly
- [ ] NATS events still trigger task completion
- [ ] UI shows correct claim status
- [ ] Backward compatibility maintained

## Notes
- This change only affects tasks with TaskIdentifier = MEME_TRADE_DAILY
- All other task types maintain their existing behavior
- The change is backward compatible - existing APIs still work
- Users will need to manually claim Meme trade rewards going forward
