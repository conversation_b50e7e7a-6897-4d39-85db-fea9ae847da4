package repo

import (
	"time"

	"github.com/google/uuid"
	"github.com/shopspring/decimal"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
	"gorm.io/gorm"
)

// RewardClaimResultRepository handles database operations for reward claim results
type RewardClaimResultRepository struct {
	db *gorm.DB
}

// NewRewardClaimResultRepository creates a new repository instance
func NewRewardClaimResultRepository() *RewardClaimResultRepository {
	return &RewardClaimResultRepository{
		db: global.GVA_DB,
	}
}

// Create saves a new reward claim result record
// If a record with the same ID already exists, it will be updated instead
func (r *RewardClaimResultRepository) Create(record *model.RewardClaimResultRecord) error {
	return r.db.Create(record).Error
}

// Update updates an existing reward claim result record
func (r *RewardClaimResultRepository) Update(record *model.RewardClaimResultRecord) error {
	return r.db.Save(record).Error
}

// GetByID retrieves a reward claim result by ID
func (r *RewardClaimResultRepository) GetByID(id uuid.UUID) (*model.RewardClaimResultRecord, error) {
	var record model.RewardClaimResultRecord
	err := r.db.Where("id = ?", id).First(&record).Error
	if err != nil {
		return nil, err
	}
	return &record, nil
}

// GetByUserID retrieves all reward claim results for a user
func (r *RewardClaimResultRepository) GetByUserID(userID uuid.UUID, limit, offset int) ([]model.RewardClaimResultRecord, error) {
	var records []model.RewardClaimResultRecord
	query := r.db.Where("user_id = ?", userID).Order("processed_at DESC")

	if limit > 0 {
		query = query.Limit(limit)
	}
	if offset > 0 {
		query = query.Offset(offset)
	}

	err := query.Find(&records).Error
	return records, err
}

// GetByUserIDAndResult retrieves reward claim results for a user with specific result
func (r *RewardClaimResultRepository) GetByUserIDAndResult(userID uuid.UUID, result model.RewardClaimResult, limit, offset int) ([]model.RewardClaimResultRecord, error) {
	var records []model.RewardClaimResultRecord
	query := r.db.Where("user_id = ? AND result = ?", userID, result).Order("processed_at DESC")

	if limit > 0 {
		query = query.Limit(limit)
	}
	if offset > 0 {
		query = query.Offset(offset)
	}

	err := query.Find(&records).Error
	return records, err
}

// GetByToken retrieves reward claim results by token
func (r *RewardClaimResultRepository) GetByToken(token string, limit, offset int) ([]model.RewardClaimResultRecord, error) {
	var records []model.RewardClaimResultRecord
	query := r.db.Where("token = ?", token).Order("processed_at DESC")

	if limit > 0 {
		query = query.Limit(limit)
	}
	if offset > 0 {
		query = query.Offset(offset)
	}

	err := query.Find(&records).Error
	return records, err
}

// GetByChainID retrieves reward claim results by chain ID
func (r *RewardClaimResultRepository) GetByChainID(chainID int, limit, offset int) ([]model.RewardClaimResultRecord, error) {
	var records []model.RewardClaimResultRecord
	query := r.db.Where("chain_id = ?", chainID).Order("processed_at DESC")

	if limit > 0 {
		query = query.Limit(limit)
	}
	if offset > 0 {
		query = query.Offset(offset)
	}

	err := query.Find(&records).Error
	return records, err
}

// GetByDateRange retrieves reward claim results within a date range
func (r *RewardClaimResultRepository) GetByDateRange(startDate, endDate time.Time, limit, offset int) ([]model.RewardClaimResultRecord, error) {
	var records []model.RewardClaimResultRecord
	query := r.db.Where("processed_at BETWEEN ? AND ?", startDate, endDate).Order("processed_at DESC")

	if limit > 0 {
		query = query.Limit(limit)
	}
	if offset > 0 {
		query = query.Offset(offset)
	}

	err := query.Find(&records).Error
	return records, err
}

// GetFailedClaims retrieves all failed reward claim results
func (r *RewardClaimResultRepository) GetFailedClaims(limit, offset int) ([]model.RewardClaimResultRecord, error) {
	var records []model.RewardClaimResultRecord
	query := r.db.Where("result = ?", model.RewardClaimResultFailed).Order("processed_at DESC")

	if limit > 0 {
		query = query.Limit(limit)
	}
	if offset > 0 {
		query = query.Offset(offset)
	}

	err := query.Find(&records).Error
	return records, err
}

// GetSuccessfulClaims retrieves all successful reward claim results
func (r *RewardClaimResultRepository) GetSuccessfulClaims(limit, offset int) ([]model.RewardClaimResultRecord, error) {
	var records []model.RewardClaimResultRecord
	query := r.db.Where("result = ?", model.RewardClaimResultSuccess).Order("processed_at DESC")

	if limit > 0 {
		query = query.Limit(limit)
	}
	if offset > 0 {
		query = query.Offset(offset)
	}

	err := query.Find(&records).Error
	return records, err
}

// GetTotalAmountByUserAndToken calculates total amount claimed by user and token
func (r *RewardClaimResultRepository) GetTotalAmountByUserAndToken(userID uuid.UUID, token string) (decimal.Decimal, error) {
	var total decimal.Decimal
	err := r.db.Model(&model.RewardClaimResultRecord{}).
		Where("user_id = ? AND token = ? AND result = ?", userID, token, model.RewardClaimResultSuccess).
		Select("COALESCE(SUM(amount), 0)").
		Scan(&total).Error
	return total, err
}

// GetTotalAmountByUser calculates total amount claimed by user across all tokens
func (r *RewardClaimResultRepository) GetTotalAmountByUser(userID uuid.UUID) (decimal.Decimal, error) {
	var total decimal.Decimal
	err := r.db.Model(&model.RewardClaimResultRecord{}).
		Where("user_id = ? AND result = ?", userID, model.RewardClaimResultSuccess).
		Select("COALESCE(SUM(amount), 0)").
		Scan(&total).Error
	return total, err
}

// CountByUserID counts total records for a user
func (r *RewardClaimResultRepository) CountByUserID(userID uuid.UUID) (int64, error) {
	var count int64
	err := r.db.Model(&model.RewardClaimResultRecord{}).
		Where("user_id = ?", userID).
		Count(&count).Error
	return count, err
}

// CountByResult counts total records by result status
func (r *RewardClaimResultRepository) CountByResult(result model.RewardClaimResult) (int64, error) {
	var count int64
	err := r.db.Model(&model.RewardClaimResultRecord{}).
		Where("result = ?", result).
		Count(&count).Error
	return count, err
}
